// MIT/Apache2 License

//! Window class registration.

use std::ffi::OsStr;
use std::os::windows::ffi::OsStrExt;
use std::panic;
use std::ptr;

use windows_sys::Win32::Foundation::{HINSTANCE, HWND, LPARAM, LRESULT, WPARAM};
use windows_sys::Win32::Graphics::Gdi::HBRUSH;
use windows_sys::Win32::System::LibraryLoader::GetModuleHandleW;
use windows_sys::Win32::UI::WindowsAndMessaging::{
    RegisterClassExW, WNDCLASSEXW, CS_HREDRAW, CS_VREDRAW, HCURSOR, HICON,
    DefWindowProcW, GetWindowLongPtrW, GWLP_USERDATA,
};

use crate::{Error, Result};

/// Type alias for window procedure callbacks.
pub type WindowProc = Box<dyn Fn(HWND, u32, WPARAM, LPARAM) -> LRESULT + Send + Sync>;

/// Universal window procedure that calls the Rust callback.
///
/// This function retrieves the callback from the window's user data and calls it,
/// handling panics appropriately.
unsafe extern "system" fn universal_window_proc(
    hwnd: HWND,
    msg: u32,
    wparam: WPARAM,
    lparam: LPARAM,
) -> LRESULT {
    // Retrieve the callback from the window's user data
    let callback_ptr = unsafe { GetWindowLongPtrW(hwnd, GWLP_USERDATA) };

    if callback_ptr == 0 {
        // No callback set, use default window procedure
        return unsafe { DefWindowProcW(hwnd, msg, wparam, lparam) };
    }

    // SAFETY: We stored a valid pointer to a WindowProc in SetWindowLongPtrW
    let callback = unsafe { &*(callback_ptr as *const WindowProc) };

    // Call the Rust callback with panic safety
    match panic::catch_unwind(panic::AssertUnwindSafe(|| {
        callback(hwnd, msg, wparam, lparam)
    })) {
        Ok(result) => result,
        Err(_) => {
            // Panic occurred in callback, use default behavior
            unsafe { DefWindowProcW(hwnd, msg, wparam, lparam) }
        }
    }
}

/// A registered window class.
pub struct WindowClass {
    /// The class name (as UTF-16).
    class_name: Vec<u16>,
    /// The instance handle.
    hinstance: HINSTANCE,
    /// The window procedure callback.
    window_proc: WindowProc,
}

/// Builder for creating and registering a window class.
pub struct WindowClassBuilder {
    class_name: Option<String>,
    window_proc: Option<WindowProc>,
    style: u32,
    class_extra: i32,
    window_extra: i32,
    hinstance: Option<HINSTANCE>,
    hicon: HICON,
    hcursor: HCURSOR,
    hbr_background: HBRUSH,
    menu_name: Option<Vec<u16>>,
    hicon_sm: HICON,
}

impl WindowClassBuilder {
    /// Create a new window class builder.
    pub fn new() -> Self {
        Self {
            class_name: None,
            window_proc: None,
            style: CS_HREDRAW | CS_VREDRAW,
            class_extra: 0,
            window_extra: 0,
            hinstance: None,
            hicon: ptr::null_mut(),
            hcursor: ptr::null_mut(),
            hbr_background: ptr::null_mut(),
            menu_name: None,
            hicon_sm: ptr::null_mut(),
        }
    }

    /// Set the class name.
    pub fn class_name(mut self, name: impl Into<String>) -> Self {
        self.class_name = Some(name.into());
        self
    }

    /// Set the window procedure callback.
    pub fn window_proc<F>(mut self, proc: F) -> Self
    where
        F: Fn(HWND, u32, WPARAM, LPARAM) -> LRESULT + Send + Sync + 'static,
    {
        self.window_proc = Some(Box::new(proc));
        self
    }

    /// Set the class style.
    pub fn style(mut self, style: u32) -> Self {
        self.style = style;
        self
    }

    /// Set the number of extra bytes to allocate following the window-class structure.
    pub fn class_extra(mut self, extra: i32) -> Self {
        self.class_extra = extra;
        self
    }

    /// Set the number of extra bytes to allocate following the window instance.
    pub fn window_extra(mut self, extra: i32) -> Self {
        self.window_extra = extra;
        self
    }

    /// Set the instance handle.
    pub fn hinstance(mut self, hinstance: HINSTANCE) -> Self {
        self.hinstance = Some(hinstance);
        self
    }

    /// Set the class icon.
    pub fn hicon(mut self, hicon: HICON) -> Self {
        self.hicon = hicon;
        self
    }

    /// Set the class cursor.
    pub fn hcursor(mut self, hcursor: HCURSOR) -> Self {
        self.hcursor = hcursor;
        self
    }

    /// Set the class background brush.
    pub fn hbr_background(mut self, hbrush: HBRUSH) -> Self {
        self.hbr_background = hbrush;
        self
    }

    /// Set the small icon.
    pub fn hicon_sm(mut self, hicon_sm: HICON) -> Self {
        self.hicon_sm = hicon_sm;
        self
    }

    /// Register the window class.
    pub fn register(mut self) -> Result<WindowClass> {
        let class_name = self.class_name.ok_or_else(|| {
            Error::from_win32_error(87) // ERROR_INVALID_PARAMETER
        })?;

        let window_proc = self.window_proc.ok_or_else(|| {
            Error::from_win32_error(87) // ERROR_INVALID_PARAMETER
        })?;

        let hinstance = self.hinstance.unwrap_or_else(|| {
            // SAFETY: GetModuleHandleW with null is safe
            unsafe { GetModuleHandleW(ptr::null()) as HINSTANCE }
        });

        // Ensure we have enough space to store a pointer to the callback
        if self.window_extra < std::mem::size_of::<*const WindowProc>() as i32 {
            self.window_extra = std::mem::size_of::<*const WindowProc>() as i32;
        }

        // Convert class name to UTF-16
        let class_name_wide: Vec<u16> = OsStr::new(&class_name)
            .encode_wide()
            .chain(std::iter::once(0))
            .collect();

        let menu_name_ptr = self.menu_name.as_ref()
            .map(|name| name.as_ptr())
            .unwrap_or(ptr::null());

        let wndclass = WNDCLASSEXW {
            cbSize: std::mem::size_of::<WNDCLASSEXW>() as u32,
            style: self.style,
            lpfnWndProc: Some(universal_window_proc),
            cbClsExtra: self.class_extra,
            cbWndExtra: self.window_extra,
            hInstance: hinstance,
            hIcon: self.hicon,
            hCursor: self.hcursor,
            hbrBackground: self.hbr_background,
            lpszMenuName: menu_name_ptr,
            lpszClassName: class_name_wide.as_ptr(),
            hIconSm: self.hicon_sm,
        };

        // SAFETY: We've properly initialized the WNDCLASSEXW structure
        let atom = unsafe { RegisterClassExW(&wndclass) };

        if atom == 0 {
            return Err(Error::last_error());
        }

        Ok(WindowClass {
            class_name: class_name_wide,
            hinstance,
            window_proc,
        })
    }
}

impl WindowClass {
    /// Get the class name as a null-terminated UTF-16 string.
    pub fn class_name_wide(&self) -> &[u16] {
        &self.class_name
    }

    /// Get the instance handle.
    pub fn hinstance(&self) -> HINSTANCE {
        self.hinstance
    }

    /// Get a reference to the window procedure.
    pub fn window_proc(&self) -> &WindowProc {
        &self.window_proc
    }
}

impl Default for WindowClassBuilder {
    fn default() -> Self {
        Self::new()
    }
}


