// MIT/Apache2 License

//! Error handling for porcupine.

use std::fmt;

use windows_sys::Win32::Foundation::{GetLastError, WIN32_ERROR};

/// Result type for porcupine operations.
pub type Result<T> = std::result::Result<T, Error>;

/// Error type for porcupine operations.
#[derive(Debug, Clone, PartialEq, Eq)]
pub struct Error {
    /// The Windows error code.
    code: WIN32_ERROR,
}

impl Error {
    /// Create a new error from a Windows error code.
    pub fn from_win32_error(code: WIN32_ERROR) -> Self {
        Self { code }
    }

    /// Create an error from the last Windows error.
    pub fn last_error() -> Self {
        // SAFETY: GetLastError is always safe to call
        let code = unsafe { GetLastError() };
        Self::from_win32_error(code)
    }

    /// Get the Windows error code.
    pub fn code(&self) -> WIN32_ERROR {
        self.code
    }

    /// Check if this error indicates success (code 0).
    pub fn is_success(&self) -> bool {
        self.code == 0
    }
}

impl fmt::Display for Error {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "Windows error code: {}", self.code)
    }
}

impl std::error::Error for Error {}

impl From<WIN32_ERROR> for Error {
    fn from(code: WIN32_ERROR) -> Self {
        Self::from_win32_error(code)
    }
}
