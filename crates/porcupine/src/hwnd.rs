// MIT/Apache2 License

//! HWND wrapper types similar to OwnedFd/BorrowedFd.

use std::fmt;
use std::marker::PhantomData;

use windows_sys::Win32::Foundation::HWND;
use windows_sys::Win32::UI::WindowsAndMessaging::DestroyWindow;

/// An owned HWND handle.
///
/// This type represents an owned window handle that will automatically
/// destroy the window when dropped.
#[derive(Debug)]
pub struct OwnedHwnd {
    hwnd: HWND,
}

/// A borrowed HWND handle.
///
/// This type represents a borrowed reference to a window handle.
/// It does not own the handle and will not destroy it when dropped.
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)]
pub struct BorrowedHwnd<'a> {
    hwnd: HWND,
    _phantom: PhantomData<&'a HWND>,
}

impl OwnedHwnd {
    /// Create a new `OwnedHwnd` from a raw HWND.
    ///
    /// # Safety
    ///
    /// The caller must ensure that:
    /// - `hwnd` is a valid window handle
    /// - The caller has ownership of the window and it's safe to destroy it
    /// - No other code will destroy this window
    pub unsafe fn from_raw_hwnd(hwnd: HWND) -> Self {
        Self { hwnd }
    }

    /// Extract the raw HWND, consuming this `OwnedHwnd`.
    ///
    /// This function transfers ownership of the window handle to the caller.
    /// The window will not be automatically destroyed.
    pub fn into_raw_hwnd(self) -> HWND {
        let hwnd = self.hwnd;
        std::mem::forget(self);
        hwnd
    }

    /// Get a borrowed reference to this HWND.
    pub fn as_hwnd(&self) -> BorrowedHwnd<'_> {
        BorrowedHwnd {
            hwnd: self.hwnd,
            _phantom: PhantomData,
        }
    }

    /// Get the raw HWND value.
    pub fn as_raw_hwnd(&self) -> HWND {
        self.hwnd
    }
}

impl<'a> BorrowedHwnd<'a> {
    /// Create a new `BorrowedHwnd` from a raw HWND.
    ///
    /// # Safety
    ///
    /// The caller must ensure that:
    /// - `hwnd` is a valid window handle
    /// - The window remains valid for the lifetime `'a`
    pub unsafe fn borrow_raw(hwnd: HWND) -> Self {
        Self {
            hwnd,
            _phantom: PhantomData,
        }
    }

    /// Get the raw HWND value.
    pub fn as_raw_hwnd(self) -> HWND {
        self.hwnd
    }
}

impl Drop for OwnedHwnd {
    fn drop(&mut self) {
        // SAFETY: We own this HWND and it should be valid
        unsafe {
            DestroyWindow(self.hwnd);
        }
    }
}

impl fmt::Display for OwnedHwnd {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "OwnedHwnd({:p})", self.hwnd)
    }
}

impl<'a> fmt::Display for BorrowedHwnd<'a> {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "BorrowedHwnd({:p})", self.hwnd)
    }
}

// Conversion traits
impl<'a> From<&'a OwnedHwnd> for BorrowedHwnd<'a> {
    fn from(owned: &'a OwnedHwnd) -> Self {
        owned.as_hwnd()
    }
}

impl From<OwnedHwnd> for HWND {
    fn from(owned: OwnedHwnd) -> Self {
        owned.into_raw_hwnd()
    }
}

impl From<BorrowedHwnd<'_>> for HWND {
    fn from(borrowed: BorrowedHwnd<'_>) -> Self {
        borrowed.as_raw_hwnd()
    }
}
