// MIT/Apache2 License

//! Window creation and management.

use std::ffi::OsStr;
use std::os::windows::ffi::OsStrExt;
use std::ptr;

use windows_sys::Win32::Foundation::{HINSTANCE, HWND, LPARAM};
use windows_sys::Win32::UI::WindowsAndMessaging::{
    CreateWindowExW as CreateWindowExWA<PERSON>, CW_USEDEFAULT, WS_OVERLAPPEDWINDOW, WS_VISIBLE,
};

use crate::{Error, OwnedHwnd, Result, WindowClass};

/// Builder for creating windows with CreateWindowExW.
#[derive(Debug)]
pub struct WindowBuilder {
    ex_style: u32,
    class_name: Option<Vec<u16>>,
    window_name: Option<Vec<u16>>,
    style: u32,
    x: i32,
    y: i32,
    width: i32,
    height: i32,
    hwnd_parent: HWND,
    hmenu: HWND, // HMENU is typedef'd to HWND
    hinstance: Option<HINSTANCE>,
    lp_param: LPARAM,
}

impl WindowBuilder {
    /// Create a new window builder.
    pub fn new() -> Self {
        Self {
            ex_style: 0,
            class_name: None,
            window_name: None,
            style: WS_OVERLAPPEDWINDOW | WS_VISIBLE,
            x: CW_USEDEFAULT,
            y: CW_USEDEFAULT,
            width: CW_USEDEFAULT,
            height: CW_USEDEFAULT,
            hwnd_parent: ptr::null_mut(),
            hmenu: ptr::null_mut(),
            hinstance: None,
            lp_param: 0,
        }
    }

    /// Set the extended window style.
    pub fn ex_style(mut self, ex_style: u32) -> Self {
        self.ex_style = ex_style;
        self
    }

    /// Set the window class name.
    pub fn class_name(mut self, class_name: impl AsRef<str>) -> Self {
        let wide: Vec<u16> = OsStr::new(class_name.as_ref())
            .encode_wide()
            .chain(std::iter::once(0))
            .collect();
        self.class_name = Some(wide);
        self
    }

    /// Set the window class from a registered WindowClass.
    pub fn window_class(mut self, class: &WindowClass) -> Self {
        self.class_name = Some(class.class_name_wide().to_vec());
        if self.hinstance.is_none() {
            self.hinstance = Some(class.hinstance());
        }
        self
    }

    /// Set the window name (title).
    pub fn window_name(mut self, window_name: impl AsRef<str>) -> Self {
        let wide: Vec<u16> = OsStr::new(window_name.as_ref())
            .encode_wide()
            .chain(std::iter::once(0))
            .collect();
        self.window_name = Some(wide);
        self
    }

    /// Set the window style.
    pub fn style(mut self, style: u32) -> Self {
        self.style = style;
        self
    }

    /// Set the window position.
    pub fn position(mut self, x: i32, y: i32) -> Self {
        self.x = x;
        self.y = y;
        self
    }

    /// Set the window size.
    pub fn size(mut self, width: i32, height: i32) -> Self {
        self.width = width;
        self.height = height;
        self
    }

    /// Set the parent window.
    pub fn parent(mut self, hwnd_parent: HWND) -> Self {
        self.hwnd_parent = hwnd_parent;
        self
    }

    /// Set the menu handle.
    pub fn menu(mut self, hmenu: HWND) -> Self {
        self.hmenu = hmenu;
        self
    }

    /// Set the instance handle.
    pub fn hinstance(mut self, hinstance: HINSTANCE) -> Self {
        self.hinstance = Some(hinstance);
        self
    }

    /// Set the lParam for WM_CREATE.
    pub fn lp_param(mut self, lp_param: LPARAM) -> Self {
        self.lp_param = lp_param;
        self
    }

    /// Create the window.
    pub fn build(self) -> Result<OwnedHwnd> {
        let class_name = self.class_name.ok_or_else(|| {
            Error::from_win32_error(87) // ERROR_INVALID_PARAMETER
        })?;

        let window_name_ptr = self.window_name.as_ref()
            .map(|name| name.as_ptr())
            .unwrap_or(ptr::null());

        let hinstance = self.hinstance.unwrap_or_else(|| {
            // SAFETY: GetModuleHandleW with null is safe
            unsafe {
                windows_sys::Win32::System::LibraryLoader::GetModuleHandleW(ptr::null()) as HINSTANCE
            }
        });

        // SAFETY: We've properly set up all the parameters for CreateWindowExW
        let hwnd = unsafe {
            CreateWindowExWApi(
                self.ex_style,
                class_name.as_ptr(),
                window_name_ptr,
                self.style,
                self.x,
                self.y,
                self.width,
                self.height,
                self.hwnd_parent,
                self.hmenu,
                hinstance,
                self.lp_param as *mut std::ffi::c_void,
            )
        };

        if hwnd.is_null() {
            return Err(Error::last_error());
        }

        // SAFETY: We just created this HWND and own it
        Ok(unsafe { OwnedHwnd::from_raw_hwnd(hwnd) })
    }
}

impl Default for WindowBuilder {
    fn default() -> Self {
        Self::new()
    }
}


