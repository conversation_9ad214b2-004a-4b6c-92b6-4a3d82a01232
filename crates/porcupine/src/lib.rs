// MIT/Apache2 License

//! Safe wrapper around the Windows winuser API.
//!
//! This crate provides safe, minimal wrappers around Windows API functions
//! for window creation and management.

#![cfg(windows)]
#![deny(unsafe_op_in_unsafe_fn)]

mod error;
mod hwnd;
mod window;
mod class;

pub use error::{Error, Result};
pub use hwnd::{BorrowedHwnd, OwnedHwnd};
pub use window::{WindowBuilder};
pub use class::{WindowClass, WindowClassBuilder};

