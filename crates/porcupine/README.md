# Porcupine

A safe, minimal wrapper around the Windows winuser API for window creation and management.

## Features

- **Safe HWND wrappers**: `OwnedHwnd` and `BorrowedHwnd` similar to `OwnedFd` and `BorrowedFd` in the standard library
- **Window class registration**: Safe wrapper around `RegisterClassExW`
- **Window creation**: Safe wrapper around `CreateWindowExW`
- **Proper error handling**: Windows error codes are properly handled and converted to Rust errors
- **Minimal overhead**: Thin wrappers that maintain safety while staying close to the underlying API

## Usage

### Registering a Window Class

```rust
use porcupine::WindowClassBuilder;

let window_class = WindowClassBuilder::new()
    .class_name("MyWindowClass")
    .window_proc(|hwnd, msg, wparam, lparam| {
        // Your window procedure logic here
        match msg {
            // Handle messages...
            _ => unsafe {
                windows_sys::Win32::UI::WindowsAndMessaging::DefWindowProcW(
                    hwnd, msg, wparam, lparam
                )
            },
        }
    })
    .register()?;
```

### Creating a Window

```rust
use porcupine::WindowBuilder;

let window = WindowBuilder::new()
    .window_class(window_class)  // Takes ownership of the window class
    .window_name("My Window")
    .size(800, 600)
    .build()?;
```

### HWND Management

The crate provides safe wrappers for HWND handles:

- `OwnedHwnd`: Owns the window handle and automatically destroys the window when dropped
- `BorrowedHwnd`: A borrowed reference to a window handle that doesn't own it

```rust
use porcupine::{OwnedHwnd, BorrowedHwnd};

// Create an owned window
let owned_window: OwnedHwnd = WindowBuilder::new()
    .class_name("MyClass")
    .build()?;

// Get a borrowed reference
let borrowed: BorrowedHwnd = owned_window.as_hwnd();

// Extract the raw HWND if needed
let raw_hwnd = owned_window.as_raw_hwnd();
```

## Target Compatibility

This crate is designed to compile under the `x86_64-pc-windows-gnu` target and uses `windows-sys` for Windows API bindings.

## Safety

All unsafe operations are properly encapsulated within safe APIs. The crate follows Rust's safety principles:

- HWND handles are managed through RAII
- All Windows API calls are wrapped with proper error checking
- UTF-16 string conversions are handled automatically
- Memory safety is maintained throughout
- Window procedure callbacks are handled with panic safety
- Callback storage is managed automatically with proper lifetime handling

## Example

See `examples/basic_window.rs` for a complete example of creating a window with a message loop.
