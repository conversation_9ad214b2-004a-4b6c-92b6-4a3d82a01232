// MIT/Apache2 License

//! Basic window creation example.

use std::ptr;

use windows_sys::Win32::Foundation::{LPARAM, LRESULT, WPARAM};
use windows_sys::Win32::UI::WindowsAndMessaging::{
    DefWindowProcW, DispatchMessageW, GetMessageW, TranslateMessage, MSG, WM_DESTROY,
    PostQuitMessage,
};

use porcupine::{WindowBuilder, WindowClassBuilder};

// Simple window procedure
fn window_proc(
    hwnd: windows_sys::Win32::Foundation::HWND,
    msg: u32,
    wparam: WPARAM,
    lparam: LPARAM,
) -> LRESULT {
    match msg {
        WM_DESTROY => {
            unsafe { PostQuitMessage(0) };
            0
        }
        _ => unsafe { DefWindowProcW(hwnd, msg, wparam, lparam) },
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Register a window class
    let window_class = WindowClassBuilder::new()
        .class_name("PorcupineExample")
        .window_proc(window_proc)
        .register()?;

    // Create a window
    let _window = WindowBuilder::new()
        .window_class(window_class)
        .window_name("Porcupine Example Window")
        .size(800, 600)
        .build()?;

    // Simple message loop
    let mut msg = MSG {
        hwnd: ptr::null_mut(),
        message: 0,
        wParam: 0,
        lParam: 0,
        time: 0,
        pt: windows_sys::Win32::Foundation::POINT { x: 0, y: 0 },
    };

    loop {
        // SAFETY: GetMessageW is safe to call with a valid MSG pointer
        let result = unsafe { GetMessageW(&mut msg, ptr::null_mut(), 0, 0) };
        
        if result == 0 {
            // WM_QUIT received
            break;
        } else if result == -1 {
            // Error occurred
            return Err("GetMessageW failed".into());
        }

        // SAFETY: TranslateMessage and DispatchMessageW are safe with valid MSG
        unsafe {
            TranslateMessage(&msg);
            DispatchMessageW(&msg);
        }
    }

    Ok(())
}
