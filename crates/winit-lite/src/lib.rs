// MIT/Apache2 License

//! Portable interface to winuser, AppKit and Wayland.

#![forbid(unsafe_code)]

#[cfg(windows)]
pub mod windows;
#[cfg(windows)]
use windows as sys;

#[cfg(not(any(windows, target_os = "macos")))]
pub mod wayland;
#[cfg(not(any(windows, target_os = "macos")))]
use wayland as sys;

use std::io;
use std::mem;
use std::time::{Duration, Instant};

//
// Event handling
//

/// Event handler.
pub trait EventHandler {
    /// We have been woken up.
    fn wakeup(&mut self, display: &mut Display, cause: WakeupCause);

    /// We are about to wait.
    fn about_to_wait(&mut self, display: &mut Display);

    /// The user woke up the `Waker`.
    fn user(&mut self, display: &mut Display);
    
    /// A window has been configured.
    fn window_configure(&mut self, display: &mut Display, window: WindowId, width: i32, height: i32);
    
    /// A window has been closed.
    fn window_close(&mut self, display: &mut Display, window: WindowId);
}

/// Event sum type.
#[non_exhaustive]
pub enum Event {
    /// We have been woken up.
    Wakeup(WakeupCause),

    /// We are about to wait.
    AboutToWait,

    /// The user woke up the `Waker`.
    User,
}

impl<F> EventHandler for F
where
    F: FnMut(&mut Display, Event),
{
    #[inline]
    fn wakeup(&mut self, display: &mut Display, cause: WakeupCause) {
        self(display, Event::Wakeup(cause));
    }

    #[inline]
    fn about_to_wait(&mut self, display: &mut Display) {
        self(display, Event::AboutToWait);
    }

    #[inline]
    fn user(&mut self, display: &mut Display) {
        self(display, Event::User);
    }
    
    #[inline]
    fn window_configure(&mut self, display: &mut Display, window: WindowId, width: i32, height: i32) {
        self(display, Event::WindowConfigure { window, width, height });
    }
    
    #[inline]
    fn window_close(&mut self, display: &mut Display, window: WindowId) {
        self(display, Event::WindowClose { window });
    }
}

/// Cause of the wakeup.
#[derive(Debug, Clone, Copy)]
pub enum WakeupCause {
    /// First-time initialization.
    Init,

    /// The wait has been cancelled by receiving an event.
    WaitCancelled,

    /// The wait has timed out.
    WaitTimedOut,

    /// Next poll.
    Poll,
}

//
// Window attributes
//

/// Size of a window.
#[derive(Debug, Copy, Clone)]
pub enum Size {
    /// Size in terms of physical pixels.
    Physical { width: u32, height: u32 },

    /// Size in terms of logical pixels.
    Logical { width: f64, height: f64 },
}

/// Position of a window.
#[derive(Debug, Copy, Clone)]
pub enum Position {
    /// Position in terms of physical pixels.
    Physical { x: i32, y: i32 },

    /// Position in terms of logical pixels.
    Logical { x: f64, y: f64 },
}

/// Set up window attributes.
macro_rules! window_attrs {
    ($lt: lifetime: $(
        $(#[$meta:meta])*
        $fname: ident : [$borrowed_ty: ty, $owned_ty: ty]
    ),*) => {
        /// Window attribute change set.
        #[derive(Default)]
        pub struct WindowAttributes<$lt> {
            $(
                $(#[$meta])*
                pub $fname: Option<$borrowed_ty>,
            )*
        }
    }
}

window_attrs! {
    'f:

    /// Position of the window.
    position: [Position, Position],

    /// Size of the window.
    size: [Size, Size],

    /// Title of the window.
    title: [&'f str, String]
}

//
// Event polling
//

/// Waits for GUI events.
pub struct Poller<'f> {
    /// Display.
    display: Display<'f>,
}

/// Interface to the GUI system.
pub struct Display<'f> {
    /// Inner display.
    inner: sys::Display<'f>,

    /// Event loop state.
    state: EventLoopState,
}

/// Builder for a [`Poller`].
#[derive(Default)]
pub struct PollerBuilder {}

/// Unique identifier for a window.
#[derive(Debug, Copy, Clone, PartialEq, Eq, PartialOrd, Ord, Hash)]
pub struct WindowId(usize);

impl<'f> Poller<'f> {
    /// Create a new poller.
    pub fn new() -> io::Result<Self> {
        PollerBuilder::new().build()
    }

    /// Run with an event handler.
    pub fn run(&mut self, handler: impl EventHandler + 'f) -> io::Result<()> {
        self.display.run(handler)
    }
}

impl Display<'_> {
    /// Indicate we should enter polling mode.
    #[inline]
    pub fn set_poll(&mut self) {
        self.state = EventLoopState::Poll;
    }

    /// Indicate we should wait forever.
    #[inline]
    pub fn set_wait(&mut self) {
        self.state = EventLoopState::Wait;
    }

    /// Indicate we should wait for a timeout.
    #[inline]
    pub fn set_timeout(&mut self, timeout: Duration) {
        match Instant::now().checked_add(timeout) {
            Some(timeout) => self.state = EventLoopState::Timeout(timeout),
            None => self.state = EventLoopState::Wait,
        }
    }

    /// Indicate we should wait for a deadline.
    #[inline]
    pub fn set_deadline(&mut self, deadline: Instant) {
        self.state = EventLoopState::Timeout(deadline);
    }

    /// Indicate that we should exit as soon as possible.
    #[inline]
    pub fn exit(&mut self) {
        self.state = EventLoopState::Exit;
    }

    /// Create a new window.
    #[inline]
    pub fn create_window(&mut self, attrs: WindowAttributes<'_>) -> io::Result<WindowId> {
        self.inner.create_window(attrs)
    }
}

impl PollerBuilder {
    /// Create a new poller builder.
    pub fn new() -> Self {
        Self::default()
    }

    /// Create a new poller.
    pub fn build<'f>(self) -> io::Result<Poller<'f>> {
        let display = sys::Display::new()?;
        Ok(Poller {
            display: Display {
                inner: display,
                state: EventLoopState::Wait,
            },
        })
    }
}

//
// Utilities
//

/// Current event loop state.
#[derive(Debug, Copy, Clone)]
enum EventLoopState {
    /// Poll mode; don't bother waiting.
    Poll,

    /// Wait forever for events.
    Wait,

    /// Wait for a timeout.
    Timeout(Instant),

    /// Exit as soon as possible.
    Exit,
}

impl EventLoopState {
    /// Get the amount of time to wait.
    fn duration(&self) -> Option<Duration> {
        match self {
            Self::Poll => Some(Duration::ZERO),
            Self::Wait => None,
            Self::Timeout(timeout) => timeout.checked_duration_since(Instant::now()),
            Self::Exit => None,
        }
    }
}

/// Implementation of a simple arena.
///
/// Useful for defining window containers.
struct Slab<T> {
    /// Chunk of filled memory.
    entries: Vec<SlabEntry<T>>,

    /// Number of filled entries.
    filled: usize,

    /// Index of the first vacant entry.
    first_vacant: usize,
}

/// Entry in a `Slab`.
enum SlabEntry<T> {
    /// Filled entry.
    Filled(T),

    /// Vacant entry.
    ///
    /// Points to the next vacant entry.
    Vacant(usize),
}

impl<T> Slab<T> {
    /// Create a new slab.
    fn new() -> Self {
        Self {
            entries: Vec::new(),
            filled: 0,
            first_vacant: 0,
        }
    }

    /// Get the entry at the given key.
    fn get(&self, key: usize) -> Option<&T> {
        match self.entries.get(key) {
            Some(SlabEntry::Filled(value)) => Some(value),
            _ => None,
        }
    }

    /// Get a mutable reference to the entry at the given key.
    fn get_mut(&mut self, key: usize) -> Option<&mut T> {
        match self.entries.get_mut(key) {
            Some(SlabEntry::Filled(value)) => Some(value),
            _ => None,
        }
    }

    /// Insert a new entry.
    fn insert(&mut self, value: T) -> usize {
        let key = self.first_vacant;
        self.filled += 1;

        if key == self.entries.len() {
            // Expand the vector.
            self.entries.push(SlabEntry::Filled(value));
            self.first_vacant = key + 1;
        } else {
            self.first_vacant = match self.entries.get(key) {
                Some(SlabEntry::Vacant(next)) => *next,
                _ => unreachable!(),
            };
            self.entries[key] = SlabEntry::Filled(value);
        }

        key
    }

    /// Remove an entry.
    fn remove(&mut self, key: usize) -> Option<T> {
        let entry = self.entries.get_mut(key)?;

        // Swap the entry out.
        let prev = mem::replace(entry, SlabEntry::Vacant(self.first_vacant));
        if let SlabEntry::Filled(val) = prev {
            self.filled -= 1;
            self.first_vacant = key;
            Some(val)
        } else {
            None
        }
    }
}
